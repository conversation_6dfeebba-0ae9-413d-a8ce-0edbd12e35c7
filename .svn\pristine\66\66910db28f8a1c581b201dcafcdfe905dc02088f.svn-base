<!DOCTYPE html>
<html lang="en">

<head>
	<title>How to Jailbreak iOS 15 & iOS 16 on Windows</title>
	<meta name="Keywords" content="jailbreak iOS 15, 16 on Windows" />
	<meta name="Description"
		content="Compared with jailbreak iOS 12 - iOS 14 devices on Windows computers, jailbreak iOS 15-16 is a bit different. This article will illustrate you how to jailbreak iOS 15/16 on Windows." />
	<meta name="copyright" content="iSumsoft" />
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=1" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

	<link rel="preload" type="font/woff" as="font" href="/fonts/bootstrap-icons.woff" crossorigin />
	<link href="../css/style.css" rel="stylesheet" type="text/css" media="screen" />
	<link rel="stylesheet" href="../css/web-app.css" media="(max-width:1024px)" /><!--web-app-style-->

</head>

<body><progress id="reading-progress" value="0" max="100"></progress><!-- #BeginLibraryItem "/library/header.lbi" -->
<div class="header">
    <div class="container">
        <div class="row no-gutters">
            <div class="logo"><a href="https://www.isumsoft.com" title="iSumsoft"><img width="239" height="41" src="https://www.isumsoft.com/images/common/isumsoft.png" alt="iSumsoft Studio" /></a></div>
            <div class="nav-app"><span></span></div>
            <ul class="nav">
                <li>Password Recovery<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos p">
                		<div class="hidden">
	                    <div class="row">
	                        <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/password-refixer-bundle/"><i class="a"></i>Password Refixer Bundle</a>
                                <a href="https://www.isumsoft.com/windows-password-refixer/"><i class="b"></i>Windows Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/windows-7-password-refixer/"><i class="c"></i>Windows 7 Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/office-password-refixer/"><i class="d"></i>Office Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/word-password-refixer/"><i class="e"></i>Word Password Refixer</a>
	                        	
	                        	
	                        </div>
	                        <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/excel-password-refixer/"><i class="f"></i>Excel Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/powerpoint-password-refixer/"><i class="g"></i>PowerPoint Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/rar-password-refixer/"><i class="h"></i>RAR Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/zip-password-refixer/"><i class="i"></i>ZIP Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/pdf-password-refixer/"><i class="j"></i>PDF Password Refixer</a>
	                        	
	                        </div>
                            <div class="col-12 col-md-4">
                                <a href="https://www.isumsoft.com/access-password-refixer/"><i class="k"></i>Access Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/outlook-password-refixer/"><i class="l"></i>Outlook Password Refixer</a>
                                <a href="https://www.isumsoft.com/sql-password-refixer/"><i class="m"></i>SQL Password Refixer</a>
	                        	<a href="https://www.isumsoft.com/wifi-password-refixer/"><i class="n"></i>WiFi Password Refixer</a>
                                <a href="https://www.isumsoft.com/itunes-password-refixer/"><i class="o"></i>iTunes Password Refixer</a>
                            </div>
	                    </div>
	                  </div>
                  </div> 
                </li>
                <li>Backup & Cleanup<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos c">
                		<div class="hidden">
                            <a href="https://www.isumsoft.com/cloner/">
                                <img loading="lazy" src="../images/icon/cloner-38.png" width="38" height="38" alt="cloner" />
                                <span>
                                    <b>Cloner</b>
                                    <em>Disk Cloning</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/filezero/">
                                <img loading="lazy" src="../images/icon/filezero-38.png" width="38" height="38" alt="filezero" />
                                <span>
                                    <b>FileZero</b>
                                    <em>File Deletion</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/sysonusb/">
                                <img loading="lazy" src="../images/icon/sysonusb-38.png" width="38" height="38" alt="sysonusb" />
                                <span>
                                    <b>SYSOnUSB</b>
                                    <em>Copy or Install Windows to USB</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/system-refixer/">
                                <img loading="lazy" src="../images/icon/system-refixer-38.png" width="38" height="38" alt="system refixer" />
                                <span>
                                    <b>System Refixer</b>
                                    <em>Windows Cleanup</em>
                                </span>
                            </a>
                        </div>
                	</div>
                </li>
                <li>iOS & Android<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos i">
                		<div class="hidden">
											<div class="row">
	                			<div class="col-12 col-md-6">
														<a href="https://www.isumsoft.com/iphone-passcode-refixer/">
															<img loading="lazy" src="../images/icon/iphone-passcode-refixer-38.png" width="38" height="38" alt="iphone passcode refixer" />
															<span>
																	<b>iPhone Passcode Refixer</b>
																	<em>iPhone Unlocking</em>
															</span>
														</a>
                            <a href="https://www.isumsoft.com/ilock-refixer/">
															<img loading="lazy" src="../images/icon/ilock-refixer-38.png" width="38" height="38" alt="ilock refixer" />
															<span>
																<b>iLock Refixer</b>
																<em>Unlock iCloud activation lock</em>
															</span>
														</a>
                            <a href="https://www.isumsoft.com/ios-refixer/">
															<img loading="lazy" src="../images/icon/ios-refixer-38.png" width="38" height="38" alt="ios refixer" />
															<span>
																	<b>iOS Refixer</b>
																	<em>iOS System Repair</em>
															</span>
													</a>
                            <a href="https://www.isumsoft.com/backupto/">
                                <img loading="lazy" src="../images/icon/backupto-38.png" width="38" height="38" alt="backupto" />
                                <span>
                                    <b>BackupTo</b>
                                    <em>Back Up iPhone to PC</em>
                                </span>
                            </a>
                            <a href="https://www.isumsoft.com/idevice-cleaner/">
                                <img loading="lazy" src="../images/icon/idevice-cleaner-38.png" width="38" height="38" alt="idevice-cleaner" />
                                <span>
                                    <b>iDevice Cleaner</b>
                                    <em>Clean up iPhone storage</em>
                                </span>
                            </a>
													</div>
													<div class="col-12 col-md-6">
														<a href="https://www.isumsoft.com/android-password-refixer/">
															<img loading="lazy" src="../images/icon/android-38.png" width="38" height="38" alt="android password refixer" />
															<span>
																	<b>Android Password Refixer</b>
																	<em>Bypass Google FRP Lock on Android</em>
															</span>
													</a>
													<a href="https://www.isumsoft.com/android-refixer/">
														<img loading="lazy" src="../images/icon/android-refixer-38.png" width="38" height="38" alt="android refixer" />
														<span>
																<b>Android Refixer</b>
																<em>Android System Repair</em>
														</span>
													</a>
													</div>
												</div>
											</div>
                	</div>
                </li>
                <li>Data Recovery<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos u">
                		<div class="hidden">
                      <div class="row">
															<div class="col-12 col-md-6">
																<a href="https://www.isumsoft.com/office-refixer/">
																	<img loading="lazy" src="../images/icon/office-refixer-38.png" width="38" height="38" alt="office refixer" />
																	<span>
																		<b>Office Refixer</b>
																		<em>Repair corrupted MS Office file</em>
																	</span>
																</a>
																					<a href="https://www.isumsoft.com/word-refixer/">
																	<img loading="lazy" src="../images/icon/word-refixer-38.png" width="38" height="38" alt="word refixer" />
																	<span>
																		<b>Word Refixer</b>
																		<em>Repair corrupted Word file</em>
																	</span>
																</a>
																<a href="https://www.isumsoft.com/excel-refixer/">
																	<img loading="lazy" src="../images/icon/excel-refixer-38.png" width="38" height="38" alt="excel refixer" />
																	<span>
																		<b>Excel Refixer</b>
																		<em>Repair corrupted Excel file</em>
																	</span>
																</a>
																<a href="https://www.isumsoft.com/powerpoint-refixer/">
																	<img loading="lazy" src="../images/icon/powerpoint-refixer-38.png" width="38" height="38" alt="powerpoint refixer" />
																	<span>
																		<b>PowerPoint Refixer</b>
																		<em>Repair corrupted PPT file</em>
																	</span>
																</a>
															</div>
	                						<div class="col-12 col-md-6">
																<a href="https://www.isumsoft.com/data-refixer/">
																	<img loading="lazy" src="../images/icon/data-38.png" width="38" height="38" alt="data refixer" />
																	<span>
																			<b>Data Refixer</b>
																			<em>Recover lost files</em>
																	</span>
															</a>
															<a href="https://www.isumsoft.com/product-key-finder/">
                                <img loading="lazy" src="../images/icon/product-key-finder-38.png" width="38" height="38" alt="product key finder" />
                                <span>
                                    <b>Product Key Finder</b>
                                    <em>Recover software product key</em>
                                </span>
                            </a>
															</div>
											</div>
	                	</div>	
                	</div>
                </li>
                <li>More Tools<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos m">
                		<div class="hidden">
													<a href="https://www.isumsoft.com/bitlocker-reader/">
														<img loading="lazy" src="../images/icon/bitlocker-reader-38.png" width="38" height="38" alt="bitlocker reader" />
														<span>
																<b>BitLocker Reader</b>
																<em>Encrypt drives with BitLocker</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/bitlocker-reader-for-mac/">
														<img loading="lazy" src="../images/icon/bitlocker-reader-for-mac-38.png" width="38" height="38" alt="bitlocker reader for mac" />
														<span>
																<b>BitLocker Reader for Mac</b>
																<em>Unlock BitLocker Drives on Mac</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/usbcode/">
														<img loading="lazy" src="../images/icon/usbcode-38.png" width="38" height="38" alt="usbcode" />
														<span>
																<b>USBCode</b>
																<em>Password Protect USB Drive</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/shapeiso/">
														<img loading="lazy" src="../images/icon/shapeiso-38.png" width="38" height="38" alt="shapeiso" />
														<span>
																<b>ShapeISO</b>
																<em>Burn, Extract and Create ISO</em>
														</span>
													</a>
													<a href="https://www.isumsoft.com/products.html">
															<i class="bi bi-grid-fill"></i>
															<span>
																	<b>All Products</b>
															</span>
													</a>
	                	</div>	
                	</div>
                </li>
                <li>Support<span class="bi bi-chevron-down"></span>
                	<div class="nav-pos s">
                		<div class="hidden">
	                		<a href="https://www.isumsoft.com/support/">
	                			<i class="bi bi-person-lines-fill"></i>
	                			<span>
	                				<b>Support Center</b>
	                				<em>FAQs & Technical Support</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/downloads.html">
	                			<i class="bi bi-cloud-arrow-down-fill"></i>
	                			<span>
	                				<b>Download</b>
	                				<em>Free Download Center</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/resources/">
	                			<i class="bi bi-chat-square-text-fill"></i>
	                			<span>
	                				<b>Resources</b>
	                				<em>How-to Articles</em>
	                			</span>
	                		</a>
	                		<a href="https://www.isumsoft.com/it/">
	                			<i class="bi bi-brush-fill"></i>
	                			<span>
	                				<b>Blog</b>
	                				<em>iSumsoft IT Blog</em>
	                			</span>
	                		</a>
	                	</div>
                	</div>
                </li>
                <li claclass="nav-li"><a href="https://www.isumsoft.com/store.html"><span class="bi bi-cart4"></span></a></li>
            </ul>
        </div>
    </div>
</div>
<!-- #EndLibraryItem --><div class="bread">
		<div class="crumb"><a href="https://www.isumsoft.com/"><img loading="lazy" src="../images/common/home.png" alt="iSumsoft"
					width="16" height="14" border="0" /></a> <big> »</big> <a href="../iphone-tips/">iPhone Tips</a><big>»</big>How to Jailbreak iOS 15 & iOS 16 on Windows
		</div>
	</div>
	<div class="product-main">
		<div class="product-content">
			<div class="left">
				<h1>How to Jailbreak iOS 15 & iOS 16 on Windows</h1>
        <div class="author-box">
            <img loading="lazy" src="../images/author/charlotte-bayley.jpg" alt="Charlotte Bayley">
            <div class="author-info">
                <span><a href="../author/charlotte-bayley.html">Charlotte Bayley</a></span>
                <p>Updated: <time>January 11, 2024</time></p>
            </div>
        </div>
				<p>Starting with iOS 15, Apple has implemented the <strong>Signed System Volume (SSV)</strong> feature,
					which ensures the
					integrity and trustworthiness of the operating system.</p>
				<p>Compared with <a href="../iphone-tips/how-to-jailbreak-ios-device-on-windows.html">jailbreak iOS 12 - iOS 14 devices
						on
						Windows computers</a>, jailbreak iOS 15-16 is a bit different. This article will illustrate you
					<strong><a href="../iphone-tips/how-to-jailbreak-ios-15-16-on-windows.html">how to jailbreak iOS 15/16 on
							Windows</a></strong>.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/how-to-jailbreak-ios-15-16-on-windows.webp"
						alt="how-to-jailbreak-ios-15-16-on-windows" width="800" height="450" /></p>
				<br />

				<br>
				<ul>
					<li><a href="#part1">Part 1: Preparations</a></li>
					<li><a href="#part2">Part 2: Boot from the USB device</a></li>
					<li><a href="#part3">Part 3: Perform Pelen1x to jailbreak iOS 15/16</a>
					</li>
				</ul>
				<p><b>Tools:</b></p>
				<ul>
					<li>A USB flash driver</li>
					<li>A Windows computer</li>
					<li>An iOS device</li>
				</ul>
				<p><b>Download: </b></p>
				<ul>
					<li><a href="https://github.com/palera1n/palen1x/releases"><b>Palen1x</b></a></li>
					<li><a href="https://github.com/ventoy/Ventoy/releases"><b>Ventoy</b></a></li>
				</ul>
				<p><b>Notes:</b></p>
				<ul>
					<li>Virtual machines are not able to jailbreak.</li>
					<li>It is best to jailbreak on Intel platforms.</li>
					<li>After a rootful jailbreak and creating a fakeFS file system, the phone will reboot and need to
						be
						rooted again to boot to the created fakeFS file system. If a fakeFS file system has already been
						created, creating it again will be an error, just root jailbreak again to boot to the created
						fakeFS file system.</li>
				</ul>


				<!-- part 1 -->
				<h2 id="part1">Part 1: Preparations</h2>
				<p><b>Step 1:</b> Download and extract <b>Palen1x</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/download-palen1x.webp"
						alt="download-palen1x" width="800" height="270" /></p>
				<p><b>Step 2:</b> Download and extract <b>Ventoy</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/download-ventoy.webp"
						alt="download-ventoy" width="800" height="297" /></p>
				<p><b>Step 3:</b> Plug the USB drive into the PC, then launch <b>Ventoy2Disk.exe</b> application.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/launch-palen1x.webp"
						alt="launch-palen1x" width="800" height="394" /></p>
				<p><b>Step 4:</b> Click on <b>Yes</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/click-on-yes-to-agree-to-make-changes.webp"
						alt="click-on-yes-to-agree-to-make-changes" width="800" height="405" /></p>
				<p><b>Step 5:</b> Fron the Device menu, choose the USB driver you connected to the computer, then click
					on <b>Install</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-usb-device-and-click-on-install.webp"
						alt="select-usb-device-and-click-on-install" width="800" height="338" /></p>
				<p><b>Step 6:</b> Warned that the device will be formatted, click <b>Yes</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/click-on-yes.webp" alt="click-on-yes"
						width="800" height="338" /></p>
				<p><b>Step 7:</b> Click on <b>Yes</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/click-on-yes-again.webp"
						alt="click-on-yes-again" width="800" height="338" /></p>
				<p><b>Step 8:</b> After successful installation, click <b>Ok</b> to close the program.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/close-the-window.webp"
						alt="close-the-window" width="800" height="338" /></p>
				<p><b>Step 9:</b> Locate <b>palen1x-amd64.iso</b> image, then press <b>Ctrl + C</b> to copy the file.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/copy-iso.png" alt="copy-iso"
						width="800" height="310" /></p>
				<p><b>Step 10:</b> Paste the copied file into the USB device using <b>Ctrl + V</b>.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/paste-iso.png" alt="paste-iso"
						width="800" height="310" /></p>

				<!-- part2 -->
				<h2 id="part2">Part 2: Boot from the USB device </h2>
				<p><b>Step 1:</b> Reboot the computer and press the hotkeys to access the <b>Boot Menu</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/hotkeys-for-boot-menu.png"
						alt="hotkeys-for-boot-menu" width="800" height="177" /></p>
				<p><b>Step 2:</b> Use the Up and Down keys to choose the USB drive, then hit <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-usb-device-as-boot-device.png"
						alt="select-usb-device-as-boot-device" width="800" height="363" /></p>
				<p><b>Step 3:</b> If you enable Secure Boot on the computer, you will be prompted an error. Press
					<b>Enter</b>
					to continue.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/enter-ok-to-confirm-error.png"
						alt="enter-ok-to-confirm-error" width="800" height="456" /></p>
				<p><b>Step 4:</b> Press any key to perform MOK management in 10 seconds.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/press-any-key-for-mok-management.png"
						alt="press-any-key-for-mok-management" width="800" height="456" /></p>
				<p><b>Step 5:</b> Use the Up and Down keys to select the <b>Enroll key from disk</b>, then hit
					<b>Enter</b>.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-enroll-key-from-disk.png"
						alt="select-enroll-key-from-disk" width="800" height="456" /></p>
				<p><b>Step 6:</b> Select <b>VTOYEFI</b> and hit <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-vtoyefi.png"
						alt="select-vtoyefi" width="800" height="456" /></p>
				<p><b>Step 7:</b> Use arrow keys to select <b>ENROLL_THIS_KEY_IN_MOKMANAGER.cer</b>, then press
					<b>Enter</b>.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-enroll-this-key-in-mokmanager.png"
						alt="select-enroll-this-key-in-mokmanager" width="800" height="456" /></p>
				<p><b>Step 8:</b> Enter <b>Continue</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/continue.png" alt="select-continue"
						width="800" height="456" /></p>
				<p><b>Step 9:</b> Enter <b>Yes</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/yes.png" alt="select-yes" width="800"
						height="456" /></p>
				<p><b>Step 10:</b> Enter <b>Reboot</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/reboot.png" alt="select-reboot"
						width="800" height="456" /></p>


				<!-- part 3 -->
				<h2 id="part3">Part 3: Perform Pelen1x to jailbreak iOS 15/16</h2>
				<p><b>Step 1:</b> Connect your iPhone to the computer and access the Boot menu again. Then press Enter
					to choose <b>palen1x-amd64.iso</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-ventoy.png"
						alt="select-ventoy" width="800" height="319" /></p>
				<p><b>Step 2:</b> Select <b>Boot in grub2 mode</b> and hit <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-grub2-mode.png"
						alt="select-grub2-mode" width="800" height="421" /></p>
				<p><b>Step 3:</b> Select <b>1 palera1n</b> and press <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-paleraln.png"
						alt="select-paleraln" width="800" height="181" /></p>
				<p><b>Step 4:</b> Enter <b>2 Options</b> and press the Spacebar key to
					choose <b>Create FakeFS</b>, then press <b>Enter</b> to confirm.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/choose-create-fakefs-from-options.png"
						alt="choose-create-fakefs-from-options" width="800" height="341" /></p>
				<p><b>Step 5:</b> Enter <b>1 Start</b>. Note that the Jailbreak Type should be <b><em>Rootful</em></b>,
					and the Arguments should be <b><em>-f -c -v</em></b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/enter-start.png" alt="enter-start"
						width="800" height="189" /></p>
				<p><b>Step 6:</b> When prompted "Press Enter when ready for DFU mode", press <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/press-enter-key-for-dfu-mode.webp"
						alt="press-enter-key-for-dfu-mode" width="800" height="422" /></p>
				<p><b>Step 7:</b> Get ready to access DFU mode manually.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/get-ready.webp" alt="get-ready"
						width="800" height="448" /></p>
				<p><b>Step 8:</b> Hold <b>volume down + side button</b> for 4 seconds.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/press-down-and-side-buttons.webp"
						alt="press-down-and-side-buttons" width="800" height="443" /></p>
				<p><b>Step 9:</b> Release side button and keep holding volume down button for 10 seconds until enter DFU
					mode.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/press-side-button.webp"
						alt="press-side-button" width="800" height="445" /></p>
				<p><b>Step 10:</b> Palen1x is performing the program and it will take about 10 minutes to create fakefs.
				</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/wait-for-creating-fakefs.webp"
						alt="wait-for-creating-fakefs" width="800" height="434" /></p>
				<p><b>Step 11:</b> If your iOS device restarts, press <b>Ctrl + C</b> to exit palen1x.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/ctrl-and-c-to-exit.webp"
						alt="ctrl-and-c-to-exit" width="800" height="444" /></p>
				<p><b>Step 12:</b> Type <b><em>palera1n -f</em></b> and hit <b>Enter</b> for palen1x program, or you can
					type <b><em>reboot</em></b> and hit <b>Enter</b> to restart
					system again.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/type-paleraln-f-to-perform-again.webp"
						alt="type-paleraln-f-to-perform-again" width="800" height="443" /></p>
				<p><b>Step 13:</b> Enter <b>1 palera1n</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-paleraln-again.png"
						alt="enter-start" width="800" height="191" /></p>
				<p><b>Step 14:</b> Enter <b>Start</b>. Note that the Jailbreak Type should be <b><em>Rootful</em></b>,
					and the arguments should be <b><em>-f</em></b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/select-start-again.png"
						alt="select-start-again" width="800" height="191" /></p>
				<p><b>Step 15:</b> When prompted "Press Enter when ready for DFU mode", press <b>Enter</b>.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/press-enter-when-ready-for-dfu-mode-after-reboot.webp"
						alt="press-enter-key-for-dfu-mode" width="800" height="422" /></p>
				<p><b>Step 16:</b> Hold <b>volume down + side button</b> for 4 seconds.</p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/press-volume-down-and-side-button-together.webp"
						alt="press-down-and-side-buttons" width="800" height="443" /></p>
				<p><b>Step 17:</b> Release side button and keep holding volume down button for 10 seconds until enter
					DFU mode. </p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/hold-volume-down-button.webp"
						alt="press-volume-down-button" width="800" height="445" /></p>
				<p><b>Step 18:</b> When the message "Booting Kernel" displays on the screen, the iPhone will restart
					automatically, completing jailbreaking. </p>
				<p><img loading="lazy" src="../images/iphone-tips/how-to-jailbreak-ios-15-16-on-windows/booting-kernel.webp"
						alt="press-side-button" width="800" height="432" /></p>
				<p><b>Step 19:</b> After the successful jailbreak, you can press <b>Ctrl + C</b> to end the program,
					then type <b><em>reboot</em></b> and hit <b> Enter</b> to reboot system.
				</p>
				<div class="related-articles clearfloat">
					<h4>Related Articles</h4>
					<ul>
						<li><span><a href="../iphone-tips/restore-iphone-in-recovery-mode-without-itunes.html"><img
										data-src="../images/iphone-tips/restore-iphone-in-recovery-mode-without-itunes/restore-iphone-in-recovery-mode.png"
										alt="restore iPhone in recovery mode" width="220" height="120"></a></span>
							<a href="../iphone-tips/restore-iphone-in-recovery-mode-without-itunes.html">How to Restore iPhone in
								Recovery Mode with or without iTunes</a>
						</li>
						<li><span><a href="../ios-issues/iphone-frozen-after-ios-16-update.html"><img
										data-src="../images/ios-issues/iphone-frozen-after-ios-16-update/5-ways-to-fix-iphone-frozen-after-ios-16-update.webp"
										alt="How to Fix iPhone X/XS/11/12/13/14 Frozen during/after iOS 16 Update" /></a></span><a
								href="../ios-issues/iphone-frozen-after-ios-16-update.html">How to Fix iPhone
								X/XS/11/12/13/14 Frozen during/after iOS 16 Update</a></li>
						<li><span><a href="../iphone-tips/stop-update-ios-software-notification-reminder.html"><img
										data-src="../images/iphone-tips/stop-ios-software-update-notification/stop-software-update-reminder.png"
										alt="Stop iOS Software Update Notification Reminders" /></a></span><a
								href="../iphone-tips/stop-update-ios-software-notification-reminder.html">Stop iOS Software
								Update Notification Reminders</a></li>
						<li><span><a href="../iphone-tips/stop-ios-app-automatic-updates-over-cellular.html"><img
										data-src="../images/iphone-tips/stop-app-automatic-updates-over-cellular/app-auto-updates.png"
										alt="Stop iOS App Automatic Updates Over Cellular" /></a></span><a
								href="../iphone-tips/stop-ios-app-automatic-updates-over-cellular.html">Stop iOS App Automatic
								Updates Over Cellular</a></li>
						<li><span><a href="../ios-issues/fix-iphone-software-update-server-could-not-be-contacted.html"><img
										data-src="../images/ios-issues/fix-iphone-software-update-server-could-not-be-contacted/iphone-software-update-server-could-not-be-contacted.png"
										alt="Fixed: The iPhone Software Update Server Could Not Be Contacted" /></a></span><a
								href="../ios-issues/fix-iphone-software-update-server-could-not-be-contacted.html">Fixed: The
								iPhone Software Update Server Could Not Be Contacted</a></li>
						<li><span><a href="../iphone-tips/how-to-update-iphone.html"><img
										data-src="../images/iphone-tips/how-to-update-iphone/update-iphone.png"
										alt="4 Ways to Update iPhone to the Latest iOS Version (14.7.1)" /></a></span><a
								href="../iphone-tips/how-to-update-iphone.html">4 Ways to Update iPhone to the Latest iOS
								Version (14.7.1)</a></li>
					</ul>
				</div>
			</div><!-- #BeginLibraryItem "/library/iphone-tips-sidebar-right.lbi" -->
<div class="sidebar-right">
  <p class="title">Latest Articles</p>
  <ul class="quick-links">
    <li><a href="apple-intelligence-the-full-breakdown.html">Apple Intelligence: Features and Global Availability</a></li>
    <li><a href="iphone-mirroring-guide.html">iPhone Mirroring: All You Need to Know</a></li>
    <li><a href="iphone-16-new-features-spatial-video.html">iPhone 16 and Spatial Video: What to Know About This New Feature</a></li>
    <li><a href="how-to-restore-photos-from-icloud.html">How to restore photos from iCloud to iPhone</a></li>
    <li><a href="how-to-install-ios-17-beta-developer-and-public.html">How to Install iOS 17 Beta: Developer &amp; Public</a></li>
    <li><a href="how-to-install-ios-18-beta-on-iphone.html">Solutions to Install iOS 18 Beta on iPhone - Update iOS 17 to iOS 18</a></li>
  </ul>
  <p class="title">Hot Articles</p>
  <ul class="quick-links">
    <li><a href="uncheck-encrypt-iphone-backup-in-itunes.html">What If You Can't Uncheck Encrypt iPhone Backup in iTunes</a></li>
    <li><a href="turn-off-cellular-data-for-specific-app.html">If Cannot Turn off Cellular Data for Specific App</a></li>
    <li><a href="set-a-strong-alphanumeric-password-on-iphone-ipad.html">Set a Strong Alphanumeric Passcode on iPhone/iPad</a></li>
    <li><a href="remove-payment-method-of-apple-id.html">Remove iTunes and App Store Payment Method to None in Order to Avoid Wasting Money</a></li>
    <li><a href="power-saving-tricks-help-extend-iphone-battery-life.html">6 Power Saving Tricks Help Extend iPhone X/8/7/6/se Battery Life</a></li>
    <li><a href="how-to-print-calendar-from-iphone-ipad.html">[Ultimate Guide] How to Print Calendar from iPhone/iPad 2024</a></li>
  </ul>
</div>
<!-- #EndLibraryItem --></div>
	</div>
	<div class="clear"></div>
	<button class="totop"><span class="bi bi-chevron-up"></span></button><!-- #BeginLibraryItem "/library/footerbox.lbi" -->
<!--cookie-->
<div id="cookie" class="cookie-container">
    <div class="cookie-content">
        <div class="cookie-div">
            <h2>Cookie Settings</h2>
            <p>We use cookies to optimize your browsing experience, provide personalized content, and analyze traffic. Click "Accept" to consent. Learn more about our <a href="../company/privacy.html#cookies-policy">Privacy Policy</a>.</p>
        </div>
        <div class="button-div"> 
            <button id="btn-accept" class="cookie-accept cookie-btn">Accept</button>
            <button id="btn-reject" class="cookie-rejuct cookie-btn">Reject</button>
        </div>
    </div>
</div>
<!--footer-->
<div class="footer-box">
  <div class="container">
    <div class="footer-top dis-flex2">
      <div>
        <div><img loading="lazy" src="../images/common/isumsoft2.png" width="239" height="41" alt="logo" /></div>
        <p><a href="mailto:<EMAIL>">Support Team: support#isumsoft.com(Replace # with @)</a><a href="mailto:<EMAIL>">Sales Team: sales#isumsoft.com(Replace # with @)</a></p>
        <ul>
          <li class="title">Follow Us</li>
          <li class="follow-a"> <a href="https://www.facebook.com/iSumsoftOfficial" target="_blank" rel="nofollow"><img loading="lazy" class="follw-icon" src="../images/common/facebook-32.png" alt="facebook" ></a> <a href="https://x.com/iSumsoft" target="_blank" rel="nofollow"><img loading="lazy" class="follw-icon" src="../images/common/twitter-32.png" alt="twitter" ></a> <a href="https://www.youtube.com/channel/UCL9RaYqRcQ0f0buIzXiaNDw" target="_blank"><img loading="lazy" class="follw-icon" src="../images/common/youtube-32.png" alt="youtube" ></a> </li>
        </ul>
      </div>
      <div>
        <p class="title">Resources</p>
        <ul>
          <li><a href="https://www.isumsoft.com/unlock-iphone/">Unlock iPhone</a></li>
          <li><a href="https://www.isumsoft.com/iphone-tips/">iPhone Tips</a></li>
          <li><a href="https://www.isumsoft.com/ios-issues/">IOS Issues</a></li>
          <li><a href="https://www.isumsoft.com/mac-tips/">Mac Tips</a></li>
          <li><a href="https://www.isumsoft.com/android/">Android Phone</a></li>
          <li><a href="https://www.isumsoft.com/bitlocker/">Bitlocker</a></li>
          <li><a href="https://www.isumsoft.com/windows-tips/">Windows Tips</a></li>
        </ul>
      </div>
      <div>
        <p class="title">Resources</p>
        <ul>
          <li><a href="https://www.isumsoft.com/windows-password/">Windows Password</a></li>
          <li><a href="https://www.isumsoft.com/backup-recovery/">Data Backup & Recovery</a></li>
          <li><a href="https://www.isumsoft.com/computer-tweaks/">Computer Tweaks</a></li>
          <li><a href="https://www.isumsoft.com/rar-zip/">RAR/ZIP</a></li>
          <li><a href="https://www.isumsoft.com/office/">MS Office</a></li>
          <li><a href="https://www.isumsoft.com/pdf/">PDF Document</a></li>
          <li><a href="https://www.isumsoft.com/internet/">Internet</a></li>
        </ul>
      </div>
      <ul>
        <li class="title">Top Products</li>
        <li><a href="https://www.isumsoft.com/windows-password-refixer/">Windows Password Refixer</a></li>
        <li><a href="https://www.isumsoft.com/iphone-passcode-refixer/">iPhone Passcode Refixer</a></li>
        <li><a href="https://www.isumsoft.com/rar-password-refixer/">RAR Password Refixer</a></li>
        <li><a href="https://www.isumsoft.com/ios-refixer/">iOS Refixer</a></li>
        <li><a href="https://www.isumsoft.com/cloner/">Cloner</a></li>
      </ul>
      <ul>
        <li class="title">Company</li>
        <li><a href="https://www.isumsoft.com/company/">About</a></li>
        <li><a  href="https://www.isumsoft.com/company/contact.html">Contact</a></li>
        <li><a href="https://www.isumsoft.com/company/privacy.html">Privacy</a></li>
        <li><a href="https://www.isumsoft.com/sitemap.html">Sitemap</a></li>
      </ul>
      <ul>
        <li class="title">Help</li>
        <li><a href="https://www.isumsoft.com/it/">Blog</a></li>
        <li><a href="https://www.isumsoft.com/support/technical-faq.html">FAQs</a></li>
        <li><a href="https://www.isumsoft.com/support/">Support</a></li>
      </ul>
    </div>
    <div class="footer-bottom">
      <p>Copyright&nbsp;&copy;&nbsp;<span id="copyright"><noscript>2025</noscript></span>&nbsp;iSumsoft Studio All Rights Reserved.</p>
    </div>
  </div>
</div>
<!-- #EndLibraryItem --><script type="text/javascript" src="../js/nav.js"></script>
	<!-- Default Statcounter code for Isumsoft.com
https://www.isumsoft.com/ -->
	<script type="text/javascript">
		var sc_project = 8760806;
		var sc_invisible = 1;
		var sc_security = "1508d00f"; 
	</script>
	<script type="text/javascript" src="https://www.statcounter.com/counter/counter.js" async></script>
	<noscript>
		<div class="statcounter"><a title="website
statistics" href="https://statcounter.com/" target="_blank"><img loading="lazy" class="statcounter"
					src="https://c.statcounter.com/8760806/0/1508d00f/1/" alt="website statistics" /></a></div>
	</noscript>
	<!-- End of Statcounter Code --><!-- #BeginLibraryItem "/library/share.lbi" -->
	<!-- #EndLibraryItem -->
</body>

</html>